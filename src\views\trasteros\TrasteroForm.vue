<template>
  <div class="trastero-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Añadir' }} Trastero</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del trastero' : 'Registra un nuevo trastero en el sistema' }}</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Número/Identificador</label>
                    <input v-model="form.numero" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Planta</label>
                    <input v-model="form.planta" type="text" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Tamaño (m²)</label>
                    <input v-model="form.tamano" type="number" step="0.01" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Precio (€/mes)</label>
                    <input v-model="form.precio" type="number" step="0.01" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Estado</label>
                    <select v-model="form.estado" class="form-select" required>
                      <option value="disponible">Disponible</option>
                      <option value="ocupado">Ocupado</option>
                      <option value="mantenimiento">En mantenimiento</option>
                      <option value="reservado">Reservado</option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Características</label>
                    <textarea v-model="form.caracteristicas" class="form-control" rows="3"></textarea>
                  </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                  <router-link to="/trasteros" class="btn btn-secondary me-2">Cancelar</router-link>
                  <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    <i class="fas fa-save me-1"></i> {{ isSubmitting ? 'Guardando...' : 'Guardar' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'vue-toastification'
import { trasterosService } from '@/services/trasteros'

export default {
  name: 'TrasteroForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const route = useRoute()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      numero: '',
      planta: '',
      tamano: '',
      precio: '',
      estado: 'disponible',
      caracteristicas: ''
    })
    
    const isEdit = computed(() => !!props.id)
    
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await trasterosService.getById(props.id)
          form.value = response.data
        } catch (error) {
          toast.error('Error al cargar los datos del trastero')
        }
      }
    })
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        if (isEdit.value) {
          await trasterosService.update(props.id, form.value)
          toast.success('Trastero actualizado correctamente')
        } else {
          await trasterosService.create(form.value)
          toast.success('Trastero creado correctamente')
        }
        router.push('/trasteros')
      } catch (error) {
        const message = error.response?.data?.message || 'Error al guardar el trastero'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isEdit,
      isSubmitting,
      handleSubmit
    }
  }
}
</script>