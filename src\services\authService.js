import axios from "axios";
import { ErrorHandler } from '../services/errorHandlingService';
import { API_CONFIG } from '../config/config';
//import { useRouter } from 'vue-router';

//const router = useRouter();

export const isTokenExpired = async () => {
  try {
    const token = localStorage.getItem("token");
    const tokenExpiry = localStorage.getItem("tokenExpiry");

    console.log('isTokenExpired');
    console.log(token);

    if (!token || !tokenExpiry) {
      return true;
    }

    console.log('isTokenExpired');
    const validToken = await checkToken();
    if ( ! validToken ) {
      localStorage.removeItem("token");
      localStorage.removeItem("tokenExpiry");
      return true;
    }

    const now = new Date();
    const expiryDate = new Date(tokenExpiry);
    if (now >= expiryDate) {
      localStorage.removeItem("token");
      localStorage.removeItem("tokenExpiry");
      return true;
    }
    return false;
  } catch (error) {
    // ErrorHandler.handleError(error, {
    //       component: 'authService',
    //       action: 'isTokenExpired'
    //     });
    // console.error('Error al comprobar el token:', error.message);
    throw error;
  }
};

export const checkToken = async () => {
  try {
    const token = localStorage.getItem("token");
    if (!token) return false;

    console.log('checkToken');
    console.log(token);

    const response = await axios.post(`${API_CONFIG.URL}/sanctum/verify-token`, 
        { token },
        { headers: {
                //Authorization: `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
            //,withCredentials: true, 
        });

    await axios.get(`${API_CONFIG.URL}/sanctum/csrf-cookie`, {
      withCredentials: true,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    if (response.status === 200) {
      return true;
    } else {
      return false;
    }
  } catch (error) {
    throw error;
  }
};

const calculateExpiryDate = (minutes) => {
  const now = new Date();
  const expiryDate = new Date(now.getTime() + minutes * 60000);
  return expiryDate;
};

// Esta es la funcion que se usa en el login
export const login = async (email, password, router) => {
  try {
    await axios.get(`${API_CONFIG.URL}/sanctum/csrf-cookie`, {
      withCredentials: true,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });
    
    const xsrfCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('XSRF-TOKEN='));
    
    let xsrfToken = '';
    if (xsrfCookie) {
      xsrfToken = decodeURIComponent(xsrfCookie.split('=')[1]);
    }

    // Luego hacer el login
    const response = await axios.post(`${API_CONFIG.URL}/sanctum/get-token/`,
      { email, password },
      {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-XSRF-TOKEN': xsrfToken
        },
        withCredentials: true
      }
    );

    console.log('Login response:', response.data);

    const token = response.data.token;
    const expiryDate = calculateExpiryDate(480);
    localStorage.setItem("token", token);
    localStorage.setItem("tokenExpiry", expiryDate.toISOString());
    return token;
  } catch (error) {
    console.error("Error en login:", error);
    //console.error("Error response:", error.response?.data);
    throw error;
  }
};
