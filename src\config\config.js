/*export const API_CONFIG = {
    // URL de base non utilisée (tu peux la supprimer si inutile)
    //URL: "http://************:86",

    // ✅ Utilise le proxy local vers le backend
    API_URL: "/api",

    TIMEOUT: 30000,
    HEADERS: {
        'Accept': 'application/json',
        'Access-Control-Allow-Credentials': 'true'
    }
};
 le code en haut Fonctionne. à remettre si ci-dessous plante - Nacer */
export const API_CONFIG = {
    // ✅ Utilise localhost para compatibilidad con Laravel Sanctum
    URL: 'http://localhost:8000',
    API_URL: 'http://localhost:8000/api',
    TIMEOUT: 30000,
    HEADERS: {
        'Accept': 'application/json',
        'Access-Control-Allow-Credentials': 'true'
    }
};
