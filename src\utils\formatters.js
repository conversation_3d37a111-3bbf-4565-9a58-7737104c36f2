// Formateo de moneda
export const formatCurrency = (amount, currency = 'EUR', locale = 'es-ES') => {
  if (amount === null || amount === undefined) return '€0,00'
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Formateo de fechas
export const formatDate = (date, options = {}) => {
  if (!date) return '-'
  
  const defaultOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }
  
  const formatOptions = { ...defaultOptions, ...options }
  
  return new Date(date).toLocaleDateString('es-ES', formatOptions)
}

// Formateo de fecha y hora
export const formatDateTime = (date) => {
  if (!date) return '-'
  
  return new Date(date).toLocaleString('es-ES', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Formateo de tiempo relativo
export const formatRelativeTime = (date) => {
  if (!date) return '-'
  
  const now = new Date()
  const time = new Date(date)
  const diff = now - time
  
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)
  
  if (seconds < 60) return 'Hace un momento'
  if (minutes < 60) return `Hace ${minutes} min`
  if (hours < 24) return `Hace ${hours} h`
  if (days < 30) return `Hace ${days} días`
  if (months < 12) return `Hace ${months} meses`
  return `Hace ${years} años`
}

// Formateo de números
export const formatNumber = (number, decimals = 0) => {
  if (number === null || number === undefined) return '0'
  
  return new Intl.NumberFormat('es-ES', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number)
}

// Formateo de porcentajes
export const formatPercentage = (value, total, decimals = 1) => {
  if (!total || total === 0) return '0%'
  
  const percentage = (value / total) * 100
  return `${percentage.toFixed(decimals)}%`
}

// Formateo de tamaño de archivo
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Capitalizar primera letra
export const capitalize = (str) => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

// Formateo de texto truncado
export const truncateText = (text, maxLength = 50) => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// Formateo de estado de pago
export const formatEstadoPago = (estado) => {
  const estados = {
    'pendiente': 'Pendiente',
    'al_dia': 'Al día',
    'vencido': 'Vencido',
    'parcial': 'Parcial'
  }
  return estados[estado] || estado
}

// Formateo de tipo de propiedad
export const formatTipoPropiedad = (tipo) => {
  const tipos = {
    'App\\Trastero': 'Trastero',
    'App\\Piso': 'Piso'
  }
  return tipos[tipo] || tipo
}

// Formateo de estado de disponibilidad
export const formatDisponibilidad = (disponible) => {
  return disponible ? 'Disponible' : 'Ocupado'
}
