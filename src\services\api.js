import axios from 'axios'
import { useToast } from 'vue-toastification'
import { API_CONFIG } from '../config/config';

const token = localStorage.getItem("token");
if (!token) throw new Error("No token stored");

// Configuración base de axios para Laravel Sanctum
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  withCredentials: true, // Necesario para Laravel Sanctum
  headers: {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
})



// Interceptor para requests
api.interceptors.request.use(
  async (config) => {
    // Agregar token de autenticación si existe
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // Agregar X-XSRF-TOKEN header si existe la cookie
    const xsrfCookie = document.cookie
      .split('; ')
      .find(row => row.startsWith('XSRF-TOKEN='))
    
    if (xsrfCookie) {
      const xsrfToken = decodeURIComponent(xsrfCookie.split('=')[1])
      config.headers['X-XSRF-TOKEN'] = xsrfToken
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Interceptor para responses
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const toast = useToast()
    
    // Manejar errores comunes
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Token expirado o inválido
          localStorage.removeItem('auth_token')
          delete api.defaults.headers.common['Authorization']

          if (window.location.pathname !== '/login') {
            toast.error('Sesión expirada. Por favor, inicia sesión nuevamente.')
            window.location.href = '/login'
          }
          break

        case 419:
          // Error de token CSRF - no debería ocurrir con Sanctum
          toast.error('Error de autenticación. Por favor, recarga la página.')
          break
          
        case 403:
          toast.error('No tienes permisos para realizar esta acción')
          break
          
        case 404:
          toast.error('Recurso no encontrado')
          break
          
        case 422:
          // Errores de validación
          if (data.errors) {
            Object.values(data.errors).flat().forEach(error => {
              toast.error(error)
            })
          } else if (data.message) {
            toast.error(data.message)
          }
          break
          
        case 500:
          toast.error('Error interno del servidor')
          break
          
        default:
          toast.error(data.message || 'Ha ocurrido un error')
      }
    } else if (error.request) {
      // Error de red
      toast.error('Error de conexión. Verifica tu conexión a internet.')
    } else {
      // Error de configuración
      toast.error('Error en la configuración de la solicitud')
    }
    
    return Promise.reject(error)
  }
)

// Función para limpiar tokens CSRF obsoletos del localStorage
export const cleanupCSRFTokens = () => {
  localStorage.removeItem('csrf_token')
}

// Función para inicializar Sanctum (obtener cookie de sesión)
export const initializeSanctum = async () => {
  try {
    console.log('Inicializando Sanctum...');

    // Usar la ruta estándar de Sanctum para obtener el CSRF cookie
    const baseURL = import.meta.env.VITE_BASE_URL || 'http://localhost:8000';
    /*
    await axios({
      method: 'GET',
      url: `${baseURL}/sanctum/csrf-cookie`,
     //withCredentials: true, // IMPORTANTE: Habilitar cookies para CSRF
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        //'X-Requested-With': 'XMLHttpRequest'
      }
    });
    */

    //const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
    //const sanctumURL = 'http://localhost:8000/sanctum/csrf-cookie';

    //await axios.get(sanctumURL, {
    //  withCredentials: true
    //});

    await axios.get(`${API_CONFIG.URL}/sanctum/csrf-cookie`, {
      //withCredentials: true,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      }
    });

    console.log('Sanctum inicializado correctamente');
    return true
  } catch (error) {
    console.error('Error inicializando Sanctum:', error);
    console.error('Detalles del error:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url
    });
    return false
  }
}

// Función para verificar si Sanctum está configurado correctamente
export const verifySanctumConfig = async () => {
  try {

    console.log('verifySanctumConfig');

    const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api'
    const sanctumURL = baseURL.replace('/api', '/sanctum/csrf-cookie')

    await axios.get(sanctumURL, {
      withCredentials: true
    })

    console.log('✅ Sanctum configurado correctamente')
    return { success: true, message: 'Sanctum configurado correctamente' }
  } catch (error) {
    console.error('❌ Error de configuración de Sanctum:', error)

    let message = 'Error de configuración de Sanctum'
    if (error.code === 'ERR_NETWORK') {
      message = 'No se puede conectar al servidor. Verifica que Laravel esté ejecutándose.'
    } else if (error.response?.status === 404) {
      message = 'Endpoint /sanctum/csrf-cookie no encontrado. Verifica la instalación de Sanctum.'
    } else if (error.message.includes('CORS')) {
      message = 'Error de CORS. Verifica la configuración de CORS en Laravel.'
    }

    return { success: false, message, error }
  }
}

// Limpiar automáticamente al cargar el módulo
cleanupCSRFTokens()

export default api




