<template>
  <div class="documento-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">Subir Documento</h1>
          <p class="text-muted">Sube un nuevo documento al sistema</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Título</label>
                    <input v-model="form.titulo" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Tipo de documento</label>
                    <select v-model="form.tipo" class="form-select" required>
                      <option value="contrato">Contrato</option>
                      <option value="factura">Factura</option>
                      <option value="recibo">Recibo</option>
                      <option value="identificacion">Identificación</option>
                      <option value="otro">Otro</option>
                    </select>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Relacionado con</label>
                    <select v-model="form.tipoPropiedad" class="form-select" required>
                      <option value="">Seleccionar...</option>
                      <option :value="TIPOS_PROPIEDAD.TRASTERO">Trastero</option>
                      <option :value="TIPOS_PROPIEDAD.PISO">Piso</option>
                      <option value="edificio">Edificio</option>
                      <option value="cliente">Cliente</option>
                    </select>
                  </div>
                  <div class="col-md-6" v-if="form.tipoPropiedad">
                    <label class="form-label">ID Relacionado</label>
                    <input v-model="form.propiedadId" type="text" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Descripción</label>
                    <textarea v-model="form.descripcion" class="form-control" rows="3"></textarea>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Archivo</label>
                    <input type="file" class="form-control" @change="handleFileChange" required>
                    <small class="text-muted">
                      Formatos permitidos: PDF, JPG, JPEG, PNG, DOC, DOCX, XLS, XLSX. Tamaño máximo: 10MB.
                    </small>
                  </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                  <router-link to="/documentos" class="btn btn-secondary me-2">Cancelar</router-link>
                  <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    <i class="fas fa-upload me-1"></i> {{ isSubmitting ? 'Subiendo...' : 'Subir documento' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { documentosService } from '@/services/documentos'
import { TIPOS_PROPIEDAD, MAX_FILE_SIZE, ALLOWED_FILE_TYPES } from '@/utils/constants'

export default {
  name: 'DocumentoForm',
  setup() {
    const router = useRouter()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      titulo: '',
      tipo: '',
      tipoPropiedad: '',
      propiedadId: '',
      descripcion: '',
      archivo: null
    })
    
    const handleFileChange = (event) => {
      const file = event.target.files[0]
      
      if (!file) return
      
      // Validar tamaño
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`El archivo es demasiado grande. Tamaño máximo: ${MAX_FILE_SIZE / (1024 * 1024)}MB`)
        event.target.value = ''
        return
      }
      
      // Validar tipo
      const fileExt = file.name.split('.').pop().toLowerCase()
      if (!ALLOWED_FILE_TYPES.includes(fileExt)) {
        toast.error(`Tipo de archivo no permitido. Formatos permitidos: ${ALLOWED_FILE_TYPES.join(', ')}`)
        event.target.value = ''
        return
      }
      
      form.value.archivo = file
    }
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        const formData = new FormData()
        formData.append('titulo', form.value.titulo)
        formData.append('tipo', form.value.tipo)
        formData.append('descripcion', form.value.descripcion)
        
        if (form.value.tipoPropiedad) {
          formData.append('tipo_propiedad', form.value.tipoPropiedad)
          formData.append('propiedad_id', form.value.propiedadId)
        }
        
        formData.append('archivo', form.value.archivo)
        
        await documentosService.upload(formData)
        toast.success('Documento subido correctamente')
        router.push('/documentos')
      } catch (error) {
        const message = error.response?.data?.message || 'Error al subir el documento'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isSubmitting,
      handleFileChange,
      handleSubmit,
      TIPOS_PROPIEDAD
    }
  }
}
</script>