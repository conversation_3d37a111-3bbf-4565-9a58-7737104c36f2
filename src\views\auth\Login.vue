<template>
  <div class="login-container">
    <div class="container-fluid h-100">
      <div class="row h-100">
        <!-- Panel izquierdo con imagen/branding -->
        <div class="col-lg-6 d-none d-lg-flex login-left">
          <div class="d-flex flex-column justify-content-center align-items-center text-white">
            <h1 class="display-4 mb-4">BarnaTrasteros</h1>
            <p class="lead text-center">
              Sistema de gestión integral para alquileres de trasteros y pisos
            </p>
            <div class="features mt-4">
              <div class="feature-item mb-3">
                <i class="fas fa-warehouse me-3"></i>
                Gestión de trasteros
              </div>
              <div class="feature-item mb-3">
                <i class="fas fa-building me-3"></i>
                Gestión de pisos
              </div>
              <div class="feature-item mb-3">
                <i class="fas fa-file-invoice-dollar me-3"></i>
                Control de pagos
              </div>
              <div class="feature-item mb-3">
                <i class="fas fa-folder-open me-3"></i>
                Gestión de documentos
              </div>
            </div>
          </div>
        </div>

        <!-- Panel derecho con formulario -->
        <div class="col-lg-6 login-right">
          <div class="d-flex flex-column justify-content-center h-100 px-4 px-lg-5">
            <div class="login-form">
              <div class="text-center mb-5">
                <h2 class="mb-3">Iniciar Sesión</h2>
                <p class="text-muted">Accede a tu cuenta para continuar</p>
              </div>

              <form @submit.prevent="handleLogin">
                <div class="mb-3">
                  <label for="email" class="form-label">Email</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-envelope"></i>
                    </span>
                    <input
                      type="email"
                      class="form-control"
                      id="email"
                      v-model="form.email"
                      :class="{ 'is-invalid': errors.email }"
                      placeholder="<EMAIL>"
                    >
                  </div>
                  <div v-if="errors.email" class="invalid-feedback">
                    {{ errors.email }}
                  </div>
                </div>

                <div class="mb-4">
                  <label for="password" class="form-label">Contraseña</label>
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-lock"></i>
                    </span>
                    <input
                      :type="showPassword ? 'text' : 'password'"
                      class="form-control"
                      id="password"
                      v-model="form.password"
                      :class="{ 'is-invalid': errors.password }"
                      placeholder="Tu contraseña"
                    >
                    <button
                      type="button"
                      class="btn btn-outline-secondary"
                      @click="togglePassword"
                    >
                      <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                    </button>
                  </div>
                  <div v-if="errors.password" class="invalid-feedback">
                    {{ errors.password }}
                  </div>
                </div>

                <div class="mb-4">
                  <div class="form-check">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      id="remember"
                      v-model="form.remember"
                    >
                    <label class="form-check-label" for="remember">
                      Recordarme
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  class="btn btn-primary w-100 py-2"
                  :disabled="isLoading"
                >
                  <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
                  {{ isLoading ? 'Iniciando sesión...' : 'Iniciar Sesión' }}
                </button>
              </form>

              <div class="text-center mt-4">
                <small class="text-muted">
                  ¿Problemas para acceder? Contacta al administrador
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { login } from '@/services/authService'
import { useToast } from 'vue-toastification'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const toast = useToast()

    // Estado reactivo del formulario
    const form = reactive({
      email: '',
      password: '',
      remember: false
    })

    // Estado de errores
    const errors = ref({})

    // Estado de carga
    const isLoading = ref(false)

    // Mostrar/ocultar contraseña
    const showPassword = ref(false)

    // Función para alternar visibilidad de contraseña
    const togglePassword = () => {
      showPassword.value = !showPassword.value
    }

    // Función de validación
    const validateForm = () => {
      errors.value = {}

      if (!form.email) {
        errors.value.email = 'El email es requerido'
      } else if (!/\S+@\S+\.\S+/.test(form.email)) {
        errors.value.email = 'El email no es válido'
      }

      if (!form.password) {
        errors.value.password = 'La contraseña es requerida'
      } else if (form.password.length < 4) {
        errors.value.password = 'La contraseña debe tener al menos 4 caracteres'
      }

      return Object.keys(errors.value).length === 0
    }

    const calculateExpiryDate = (minutes) => {
      const now = new Date();
      const expiryDate = new Date(now.getTime() + minutes * 60000);
      return expiryDate;
    };

    // Función para manejar el login usando authService
    const handleLogin = async () => {
      console.log('handleLogin');
      if (!validateForm()) {
        return
      }

      isLoading.value = true
      errors.value = {}

      try {
        const token = await login(form.email, form.password)

        if (token) {

          localStorage.setItem("token", token);
          const expiryDate = calculateExpiryDate(480);
          localStorage.setItem("tokenExpiry", expiryDate.toISOString());

          toast.success('¡Bienvenido!');
          router.push('/');
        }
      } catch (error) {
        console.error('Error en login:', error)

        // Manejar errores específicos
        if (error.response?.status === 401) {
          errors.value.email = 'Credenciales incorrectas'
          errors.value.password = 'Credenciales incorrectas'
          toast.error('Email o contraseña incorrectos')
        } else if (error.response?.status === 422) {
          // Errores de validación del servidor
          const serverErrors = error.response.data.errors || {}
          Object.keys(serverErrors).forEach(key => {
            errors.value[key] = serverErrors[key][0]
          })
          toast.error('Por favor corrige los errores del formulario')
        } else {
          toast.error('Error de conexión. Inténtalo de nuevo.')
        }
      } finally {
        isLoading.value = false
      }
    }

    return {
      form,
      errors,
      showPassword,
      isLoading,
      togglePassword,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-left {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  position: relative;
}

.login-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.login-right {
  background: white;
  display: flex;
  align-items: center;
}

.feature-item {
  font-size: 1.1rem;
  opacity: 0.9;
}

.login-form {
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.input-group-text {
  background-color: #f8f9fa;
  border-right: none;
}

.form-control {
  border-left: none;
}

.form-control:focus {
  box-shadow: none;
  border-color: #007bff;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
}

@media (max-width: 991.98px) {
  .login-container {
    background: white;
  }
  
  .login-right {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}
</style>
