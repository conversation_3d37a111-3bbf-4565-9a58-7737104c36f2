<template>
  <div class="sidebar bg-light border-end">
    <div class="sidebar-sticky pt-3">
      <ul class="nav flex-column">
        <li class="nav-item">
          <router-link to="/" class="nav-link" exact-active-class="active">
            <i class="fas fa-tachometer-alt me-2"></i>
            Dashboard
          </router-link>
        </li>
        
        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Propiedades</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/trasteros" class="nav-link" active-class="active">
            <i class="fas fa-warehouse me-2"></i>
            Trasteros
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/pisos" class="nav-link" active-class="active">
            <i class="fas fa-home me-2"></i>
            Pisos
          </router-link>
        </li>
        
        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Gestión</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/clientes" class="nav-link" active-class="active">
            <i class="fas fa-users me-2"></i>
            Clientes
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/alquileres" class="nav-link" active-class="active">
            <i class="fas fa-file-contract me-2"></i>
            Alquileres
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/alquileres/nuevo" class="nav-link" active-class="active">
            <i class="fas fa-plus-circle me-2"></i>
            Nuevo Alquiler
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/pagos/nuevo" class="nav-link" active-class="active">
            <i class="fas fa-money-bill-wave me-2"></i>
            Registrar Pago
          </router-link>
        </li>
        
        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Finanzas</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/gastos" class="nav-link" active-class="active">
            <i class="fas fa-receipt me-2"></i>
            Gastos
          </router-link>
        </li>
        <li class="nav-item">
          <router-link to="/gastos/nuevo" class="nav-link" active-class="active">
            <i class="fas fa-plus-circle me-2"></i>
            Nuevo Gasto
          </router-link>
        </li>
        
        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Documentación</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/documentos" class="nav-link" active-class="active">
            <i class="fas fa-folder-open me-2"></i>
            Documentos
          </router-link>
        </li>
        
        <li class="nav-item">
          <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted">
            <span>Informes</span>
          </h6>
        </li>
        <li class="nav-item">
          <router-link to="/reportes" class="nav-link" active-class="active">
            <i class="fas fa-chart-bar me-2"></i>
            Reportes
          </router-link>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Sidebar'
}
</script>

<style scoped>
.sidebar {
  width: 250px;
  min-height: calc(100vh - 56px);
  position: sticky;
  top: 56px;
  height: calc(100vh - 56px);
  z-index: 100;
  box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
  position: sticky;
  top: 0;
  height: calc(100vh - 56px);
  padding-top: .5rem;
  overflow-x: hidden;
  overflow-y: auto;
}

.sidebar .nav-link {
  font-weight: 500;
  color: #333;
  padding: .5rem 1rem;
}

.sidebar .nav-link.active {
  color: #007bff;
  background-color: rgba(0, 123, 255, 0.1);
}

.sidebar .nav-link:hover {
  color: #007bff;
}

.sidebar-heading {
  font-size: .75rem;
  text-transform: uppercase;
}
</style>