import api from './api'

export const clientesService = {
  // Obtener todos los clientes
  getAll(params = {}) {
    return api.get('/clientes', { params })
  },

  // Obtener cliente por ID
  getById(id) {
    return api.get(`/clientes/${id}`)
  },

  // Crear nuevo cliente
  create(data) {
    return api.post('/clientes', data)
  },

  // Actualizar cliente
  update(id, data) {
    return api.put(`/clientes/${id}`, data)
  },

  // Eliminar cliente
  delete(id) {
    return api.delete(`/clientes/${id}`)
  },

  // Obtener clientes activos
  getActivos() {
    return api.get('/clientes-activos')
  },

  // Obtener posibles clientes
  getPosibles() {
    return api.get('/clientes-posibles')
  },

  // Obtener clientes confirmados
  getConfirmados() {
    return api.get('/clientes-confirmados')
  },

  // Confirmar cliente
  confirmar(id) {
    return api.patch(`/clientes/${id}/confirmar`)
  }
}
