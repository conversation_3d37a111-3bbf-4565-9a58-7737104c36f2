<template>
  <div class="pisos-list">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Gestión de Pisos</h1>
              <p class="text-muted mb-0">Administra todos los pisos de alquiler</p>
            </div>
            <router-link to="/pisos/nuevo" class="btn btn-primary">
              <i class="fas fa-plus me-1"></i>Nuevo Piso
            </router-link>
          </div>
        </div>
      </div>

      <!-- Filtros y búsqueda -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <div class="row">
                <div class="col-md-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-search"></i>
                    </span>
                    <input
                      v-model="searchTerm"
                      type="text"
                      class="form-control"
                      placeholder="Buscar pisos..."
                    >
                  </div>
                </div>
                <div class="col-md-2">
                  <select v-model="filterEstado" class="form-select">
                    <option value="">Todos los estados</option>
                    <option value="disponible">Disponible</option>
                    <option value="ocupado">Ocupado</option>
                    <option value="mantenimiento">Mantenimiento</option>
                    <option value="reservado">Reservado</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="filterHabitaciones" class="form-select">
                    <option value="">Todas las habitaciones</option>
                    <option value="1">1 habitación</option>
                    <option value="2">2 habitaciones</option>
                    <option value="3">3 habitaciones</option>
                    <option value="4">4+ habitaciones</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="filterPrecio" class="form-select">
                    <option value="">Todos los precios</option>
                    <option value="0-500">Hasta 500€</option>
                    <option value="500-800">500€ - 800€</option>
                    <option value="800-1200">800€ - 1200€</option>
                    <option value="1200+">Más de 1200€</option>
                  </select>
                </div>
                <div class="col-md-2">
                  <select v-model="sortBy" class="form-select">
                    <option value="direccion">Ordenar por dirección</option>
                    <option value="precio">Ordenar por precio</option>
                    <option value="habitaciones">Ordenar por habitaciones</option>
                    <option value="superficie">Ordenar por superficie</option>
                  </select>
                </div>
                <div class="col-md-1">
                  <button
                    class="btn btn-outline-secondary w-100"
                    @click="clearFilters"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Estadísticas rápidas -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card bg-primary text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ totalPisos }}</h4>
                  <p class="mb-0">Total Pisos</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-building fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-success text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ pisosDisponibles }}</h4>
                  <p class="mb-0">Disponibles</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-check-circle fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-danger text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ pisosOcupados }}</h4>
                  <p class="mb-0">Ocupados</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-lock fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card bg-info text-white">
            <div class="card-body">
              <div class="d-flex justify-content-between">
                <div>
                  <h4 class="mb-0">{{ formatPrice(ingresosMensuales) }}</h4>
                  <p class="mb-0">Ingresos/Mes</p>
                </div>
                <div class="align-self-center">
                  <i class="fas fa-euro-sign fa-2x"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Lista de pisos -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">
                <i class="fas fa-building me-2"></i>
                Pisos ({{ filteredPisos.length }})
              </h5>
              <div class="btn-group btn-group-sm">
                <button
                  class="btn btn-outline-primary"
                  @click="loadPisos"
                  :disabled="isLoading"
                >
                  <i class="fas fa-sync-alt me-1" :class="{ 'fa-spin': isLoading }"></i>
                  Actualizar
                </button>
                <button
                  class="btn btn-outline-secondary"
                  @click="toggleView"
                >
                  <i :class="viewMode === 'table' ? 'fas fa-th' : 'fas fa-list'"></i>
                  {{ viewMode === 'table' ? 'Vista Grid' : 'Vista Tabla' }}
                </button>
                <button class="btn btn-outline-success">
                  <i class="fas fa-download me-1"></i>Exportar
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <!-- Loading state -->
              <div v-if="isLoading" class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-3 text-muted">Cargando pisos...</p>
              </div>

              <!-- Empty state -->
              <div v-else-if="filteredPisos.length === 0" class="text-center py-5">
                <i class="fas fa-building fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No hay pisos</h5>
                <p class="text-muted">
                  {{ hasFilters ? 'No se encontraron pisos con los filtros aplicados' : 'Aún no hay pisos registrados' }}
                </p>
                <router-link v-if="!hasFilters" to="/pisos/nuevo" class="btn btn-primary">
                  <i class="fas fa-plus me-1"></i>Crear primer piso
                </router-link>
              </div>

              <!-- Vista tabla -->
              <div v-else-if="viewMode === 'table'" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th>Dirección</th>
                      <th>Habitaciones</th>
                      <th>Superficie</th>
                      <th>Precio</th>
                      <th>Estado</th>
                      <th>Cliente</th>
                      <th>Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="piso in paginatedPisos" :key="piso.id">
                      <td>
                        <div class="fw-bold">{{ piso.direccion }}</div>
                        <small class="text-muted">{{ piso.piso }} - {{ piso.ciudad }}</small>
                      </td>
                      <td>
                        <div>{{ piso.habitaciones }} hab.</div>
                        <small class="text-muted">{{ piso.banos }} baños</small>
                      </td>
                      <td>{{ piso.superficie }} m²</td>
                      <td class="fw-bold">{{ formatPrice(piso.precio) }}</td>
                      <td>
                        <span :class="getEstadoClass(piso.estado)">
                          {{ getEstadoText(piso.estado) }}
                        </span>
                      </td>
                      <td>
                        <div v-if="piso.cliente">
                          <div class="fw-bold">{{ piso.cliente.nombre }}</div>
                          <small class="text-muted">{{ piso.cliente.telefono }}</small>
                        </div>
                        <small v-else class="text-muted">Sin asignar</small>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <router-link
                            :to="`/pisos/${piso.id}`"
                            class="btn btn-outline-info"
                            title="Ver detalles"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/pisos/${piso.id}/editar`"
                            class="btn btn-outline-primary"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            class="btn btn-outline-danger"
                            @click="confirmDelete(piso)"
                            title="Eliminar"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- Vista grid -->
              <div v-else class="p-3">
                <div class="row">
                  <div v-for="piso in paginatedPisos" :key="piso.id" class="col-md-6 col-lg-4 mb-3">
                    <div class="card piso-card" :class="{ 'border-success': piso.estado === 'disponible', 'border-danger': piso.estado === 'ocupado' }">
                      <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ piso.direccion }}</h6>
                        <span :class="getEstadoClass(piso.estado)">
                          {{ getEstadoText(piso.estado) }}
                        </span>
                      </div>
                      <div class="card-body">
                        <div class="mb-2">
                          <small class="text-muted">Ubicación:</small>
                          <div>{{ piso.piso }} - {{ piso.ciudad }}</div>
                        </div>
                        <div class="row mb-2">
                          <div class="col-6">
                            <small class="text-muted">Habitaciones:</small>
                            <div class="fw-bold">{{ piso.habitaciones }}</div>
                          </div>
                          <div class="col-6">
                            <small class="text-muted">Baños:</small>
                            <div class="fw-bold">{{ piso.banos }}</div>
                          </div>
                        </div>
                        <div class="mb-2">
                          <small class="text-muted">Superficie:</small>
                          <div>{{ piso.superficie }} m²</div>
                        </div>
                        <div class="mb-2">
                          <small class="text-muted">Precio:</small>
                          <div class="fw-bold text-primary">{{ formatPrice(piso.precio) }}/mes</div>
                        </div>
                        <div v-if="piso.cliente" class="mb-2">
                          <small class="text-muted">Cliente:</small>
                          <div class="fw-bold">{{ piso.cliente.nombre }}</div>
                        </div>
                      </div>
                      <div class="card-footer">
                        <div class="btn-group w-100">
                          <router-link
                            :to="`/pisos/${piso.id}`"
                            class="btn btn-sm btn-outline-info"
                          >
                            <i class="fas fa-eye"></i>
                          </router-link>
                          <router-link
                            :to="`/pisos/${piso.id}/editar`"
                            class="btn btn-sm btn-outline-primary"
                          >
                            <i class="fas fa-edit"></i>
                          </router-link>
                          <button
                            class="btn btn-sm btn-outline-danger"
                            @click="confirmDelete(piso)"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Paginación -->
              <div v-if="totalPages > 1" class="card-footer">
                <nav aria-label="Paginación de pisos">
                  <ul class="pagination pagination-sm justify-content-center mb-0">
                    <li class="page-item" :class="{ disabled: currentPage === 1 }">
                      <button class="page-link" @click="goToPage(currentPage - 1)">
                        <i class="fas fa-chevron-left"></i>
                      </button>
                    </li>

                    <li
                      v-for="page in visiblePages"
                      :key="page"
                      class="page-item"
                      :class="{ active: page === currentPage }"
                    >
                      <button class="page-link" @click="goToPage(page)">
                        {{ page }}
                      </button>
                    </li>

                    <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                      <button class="page-link" @click="goToPage(currentPage + 1)">
                        <i class="fas fa-chevron-right"></i>
                      </button>
                    </li>
                  </ul>
                </nav>

                <div class="text-center mt-2">
                  <small class="text-muted">
                    Mostrando {{ startIndex + 1 }} - {{ Math.min(endIndex, filteredPisos.length) }}
                    de {{ filteredPisos.length }} pisos
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmación -->
    <div v-if="showDeleteModal" class="modal fade show d-block" tabindex="-1">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Confirmar eliminación</h5>
            <button
              type="button"
              class="btn-close"
              @click="showDeleteModal = false"
            ></button>
          </div>
          <div class="modal-body">
            <p>¿Estás seguro de que quieres eliminar el piso <strong>{{ pisoToDelete?.direccion }}</strong>?</p>
            <p class="text-danger">
              <i class="fas fa-exclamation-triangle me-1"></i>
              Esta acción no se puede deshacer.
            </p>
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              @click="showDeleteModal = false"
            >
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-danger"
              @click="deletePiso"
              :disabled="isDeleting"
            >
              <i class="fas fa-trash me-1"></i>
              {{ isDeleting ? 'Eliminando...' : 'Eliminar' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useToast } from 'vue-toastification'

export default {
  name: 'PisosList',
  setup() {
    const toast = useToast()

    const pisos = ref([])
    const isLoading = ref(false)
    const isDeleting = ref(false)
    const searchTerm = ref('')
    const filterEstado = ref('')
    const filterHabitaciones = ref('')
    const filterPrecio = ref('')
    const sortBy = ref('direccion')
    const currentPage = ref(1)
    const itemsPerPage = 12
    const viewMode = ref('table')
    const showDeleteModal = ref(false)
    const pisoToDelete = ref(null)

    const filteredPisos = computed(() => {
      let filtered = pisos.value

      if (searchTerm.value) {
        const term = searchTerm.value.toLowerCase()
        filtered = filtered.filter(piso =>
          piso.direccion.toLowerCase().includes(term) ||
          piso.ciudad.toLowerCase().includes(term) ||
          piso.piso.toLowerCase().includes(term) ||
          (piso.cliente && piso.cliente.nombre.toLowerCase().includes(term))
        )
      }

      if (filterEstado.value) {
        filtered = filtered.filter(piso => piso.estado === filterEstado.value)
      }

      if (filterHabitaciones.value) {
        if (filterHabitaciones.value === '4') {
          filtered = filtered.filter(piso => piso.habitaciones >= 4)
        } else {
          filtered = filtered.filter(piso => piso.habitaciones == filterHabitaciones.value)
        }
      }

      if (filterPrecio.value) {
        const [min, max] = filterPrecio.value.split('-').map(p => p.replace('+', ''))
        filtered = filtered.filter(piso => {
          if (filterPrecio.value === '1200+') {
            return piso.precio >= 1200
          } else {
            return piso.precio >= parseInt(min) && piso.precio <= parseInt(max)
          }
        })
      }

      // Ordenar
      filtered.sort((a, b) => {
        switch (sortBy.value) {
          case 'direccion':
            return a.direccion.localeCompare(b.direccion)
          case 'precio':
            return a.precio - b.precio
          case 'habitaciones':
            return a.habitaciones - b.habitaciones
          case 'superficie':
            return a.superficie - b.superficie
          default:
            return 0
        }
      })

      return filtered
    })

    const totalPisos = computed(() => pisos.value.length)
    const pisosDisponibles = computed(() => pisos.value.filter(p => p.estado === 'disponible').length)
    const pisosOcupados = computed(() => pisos.value.filter(p => p.estado === 'ocupado').length)
    const ingresosMensuales = computed(() => {
      return pisos.value
        .filter(p => p.estado === 'ocupado')
        .reduce((total, p) => total + p.precio, 0)
    })

    const hasFilters = computed(() => {
      return searchTerm.value || filterEstado.value || filterHabitaciones.value || filterPrecio.value
    })

    const totalPages = computed(() => Math.ceil(filteredPisos.value.length / itemsPerPage))
    const startIndex = computed(() => (currentPage.value - 1) * itemsPerPage)
    const endIndex = computed(() => startIndex.value + itemsPerPage)

    const paginatedPisos = computed(() => {
      return filteredPisos.value.slice(startIndex.value, endIndex.value)
    })

    const visiblePages = computed(() => {
      const pages = []
      const start = Math.max(1, currentPage.value - 2)
      const end = Math.min(totalPages.value, currentPage.value + 2)

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      return pages
    })

    const formatPrice = (price) => {
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(price)
    }

    const getEstadoClass = (estado) => {
      const classes = {
        'disponible': 'badge bg-success',
        'ocupado': 'badge bg-danger',
        'mantenimiento': 'badge bg-warning text-dark',
        'reservado': 'badge bg-info'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    const getEstadoText = (estado) => {
      const texts = {
        'disponible': 'Disponible',
        'ocupado': 'Ocupado',
        'mantenimiento': 'Mantenimiento',
        'reservado': 'Reservado'
      }
      return texts[estado] || estado
    }

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page
      }
    }

    const clearFilters = () => {
      searchTerm.value = ''
      filterEstado.value = ''
      filterHabitaciones.value = ''
      filterPrecio.value = ''
      currentPage.value = 1
    }

    const toggleView = () => {
      viewMode.value = viewMode.value === 'table' ? 'grid' : 'table'
    }

    const loadPisos = async () => {
      isLoading.value = true
      try {
        // Simular datos de pisos
        pisos.value = [
          {
            id: 1,
            direccion: 'Calle Mayor, 123',
            piso: '2º A',
            ciudad: 'Barcelona',
            habitaciones: 3,
            banos: 2,
            superficie: 85,
            precio: 950,
            estado: 'ocupado',
            cliente: { nombre: 'Ana Martín', telefono: '93 123 45 67' }
          },
          {
            id: 2,
            direccion: 'Avenida Diagonal, 456',
            piso: '4º B',
            ciudad: 'Barcelona',
            habitaciones: 2,
            banos: 1,
            superficie: 65,
            precio: 750,
            estado: 'disponible',
            cliente: null
          },
          {
            id: 3,
            direccion: 'Calle Balmes, 789',
            piso: '1º C',
            ciudad: 'Barcelona',
            habitaciones: 4,
            banos: 2,
            superficie: 110,
            precio: 1200,
            estado: 'ocupado',
            cliente: { nombre: 'Carlos López', telefono: '93 987 65 43' }
          },
          {
            id: 4,
            direccion: 'Plaza Catalunya, 12',
            piso: 'Ático',
            ciudad: 'Barcelona',
            habitaciones: 2,
            banos: 2,
            superficie: 75,
            precio: 850,
            estado: 'mantenimiento',
            cliente: null
          },
          {
            id: 5,
            direccion: 'Calle Muntaner, 345',
            piso: '3º A',
            ciudad: 'Barcelona',
            habitaciones: 1,
            banos: 1,
            superficie: 45,
            precio: 600,
            estado: 'disponible',
            cliente: null
          }
        ]
      } catch (error) {
        console.error('Error al cargar pisos:', error)
        toast.error('Error al cargar la lista de pisos')
      } finally {
        isLoading.value = false
      }
    }

    const confirmDelete = (piso) => {
      pisoToDelete.value = piso
      showDeleteModal.value = true
    }

    const deletePiso = async () => {
      if (!pisoToDelete.value) return

      isDeleting.value = true
      try {
        // await pisosService.delete(pisoToDelete.value.id)

        pisos.value = pisos.value.filter(p => p.id !== pisoToDelete.value.id)
        showDeleteModal.value = false
        pisoToDelete.value = null
        toast.success('Piso eliminado correctamente')
      } catch (error) {
        console.error('Error al eliminar piso:', error)
        toast.error('Error al eliminar el piso')
      } finally {
        isDeleting.value = false
      }
    }

    onMounted(() => {
      loadPisos()
    })

    return {
      pisos,
      isLoading,
      isDeleting,
      searchTerm,
      filterEstado,
      filterHabitaciones,
      filterPrecio,
      sortBy,
      currentPage,
      itemsPerPage,
      viewMode,
      showDeleteModal,
      pisoToDelete,
      filteredPisos,
      totalPisos,
      pisosDisponibles,
      pisosOcupados,
      ingresosMensuales,
      hasFilters,
      totalPages,
      startIndex,
      endIndex,
      paginatedPisos,
      visiblePages,
      formatPrice,
      getEstadoClass,
      getEstadoText,
      goToPage,
      clearFilters,
      toggleView,
      loadPisos,
      confirmDelete,
      deletePiso
    }
  }
}
</script>

<style scoped>
.pisos-list .card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.pisos-list .card-title {
  color: #495057;
  font-weight: 600;
}

.piso-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.piso-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.pisos-list .table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  font-size: 0.875rem;
}

.pisos-list .table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.pisos-list .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

.pisos-list .btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pisos-list .spinner-border {
  width: 2rem;
  height: 2rem;
}

.pisos-list .modal.show {
  background-color: rgba(0, 0, 0, 0.5);
}

.pisos-list .pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.pisos-list .input-group-text {
  background-color: #f8f9fa;
  border-color: #ced4da;
}

.pisos-list .card.bg-primary,
.pisos-list .card.bg-success,
.pisos-list .card.bg-danger,
.pisos-list .card.bg-info {
  border: none;
}

.pisos-list .card.bg-primary .card-body,
.pisos-list .card.bg-success .card-body,
.pisos-list .card.bg-danger .card-body,
.pisos-list .card.bg-info .card-body {
  padding: 1.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pisos-list .card-body {
    padding: 0.5rem;
  }

  .pisos-list .table-responsive {
    font-size: 0.8rem;
  }

  .pisos-list .btn-group {
    flex-direction: column;
  }

  .pisos-list .btn-group .btn {
    margin-bottom: 0.25rem;
  }

  .pisos-list .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .pisos-list .col-md-3,
  .pisos-list .col-md-2,
  .pisos-list .col-md-1 {
    margin-bottom: 0.5rem;
  }

  .piso-card {
    margin-bottom: 1rem;
  }
}

/* Print styles */
@media print {
  .pisos-list .btn,
  .pisos-list .btn-group,
  .pisos-list .pagination {
    display: none !important;
  }

  .pisos-list .card {
    border: 1px solid #000;
    break-inside: avoid;
  }

  .piso-card {
    transform: none !important;
    box-shadow: none !important;
  }
}
</style>
