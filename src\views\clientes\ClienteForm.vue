<template>
  <div class="cliente-form">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">{{ isEdit ? 'Editar' : 'Añadir' }} Cliente</h1>
          <p class="text-muted">{{ isEdit ? 'Modifica los datos del cliente' : 'Registra un nuevo cliente en el sistema' }}</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body">
              <form @submit.prevent="handleSubmit">
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Nombre</label>
                    <input v-model="form.nombre" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Apellidos</label>
                    <input v-model="form.apellidos" type="text" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">DNI/NIE</label>
                    <input v-model="form.dni" type="text" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Teléfono</label>
                    <input v-model="form.telefono" type="tel" class="form-control" required>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-md-6">
                    <label class="form-label">Email</label>
                    <input v-model="form.email" type="email" class="form-control" required>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label">Estado</label>
                    <select v-model="form.estado" class="form-select" required>
                      <option value="activo">Activo</option>
                      <option value="inactivo">Inactivo</option>
                      <option value="posible">Posible cliente</option>
                    </select>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Dirección</label>
                    <textarea v-model="form.direccion" class="form-control" rows="2"></textarea>
                  </div>
                </div>
                
                <div class="row mb-3">
                  <div class="col-12">
                    <label class="form-label">Notas</label>
                    <textarea v-model="form.notas" class="form-control" rows="3"></textarea>
                  </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                  <router-link to="/clientes" class="btn btn-secondary me-2">Cancelar</router-link>
                  <button type="submit" class="btn btn-primary" :disabled="isSubmitting">
                    <i class="fas fa-save me-1"></i> {{ isSubmitting ? 'Guardando...' : 'Guardar' }}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'vue-toastification'
import { clientesService } from '@/services/clientes'

export default {
  name: 'ClienteForm',
  props: {
    id: {
      type: String,
      required: false,
      default: null
    }
  },
  setup(props) {
    const router = useRouter()
    const toast = useToast()
    
    const isSubmitting = ref(false)
    const form = ref({
      nombre: '',
      apellidos: '',
      dni: '',
      telefono: '',
      email: '',
      direccion: '',
      estado: 'posible',
      notas: ''
    })
    
    const isEdit = computed(() => !!props.id)
    
    onMounted(async () => {
      if (isEdit.value) {
        try {
          const response = await clientesService.getById(props.id)
          form.value = response.data
        } catch (error) {
          toast.error('Error al cargar los datos del cliente')
        }
      }
    })
    
    const handleSubmit = async () => {
      isSubmitting.value = true
      
      try {
        if (isEdit.value) {
          await clientesService.update(props.id, form.value)
          toast.success('Cliente actualizado correctamente')
        } else {
          await clientesService.create(form.value)
          toast.success('Cliente creado correctamente')
        }
        router.push('/clientes')
      } catch (error) {
        const message = error.response?.data?.message || 'Error al guardar el cliente'
        toast.error(message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    return {
      form,
      isEdit,
      isSubmitting,
      handleSubmit
    }
  }
}
</script>