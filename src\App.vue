<template>
  <div id="app">
    <NavBar />
    <div class="d-flex">
      <!-- Sidebar (solo visible en rutas autenticadas) -->
      <Sidebar v-if="isAuthenticated" />
      
      <!-- Main content -->
      <div class="main-content flex-grow-1">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import NavBar from '@/components/layout/NavBar.vue'
import Sidebar from '@/components/layout/Sidebar.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    Sidebar
  },
  setup() {
    const authStore = useAuthStore()
    const isAuthenticated = computed(() => authStore.isAuthenticated)
    
    return {
      isAuthenticated
    }
  }
}
</script>

<style>
.main-content {
  padding: 1.5rem;
  min-height: calc(100vh - 56px);
}
</style>

