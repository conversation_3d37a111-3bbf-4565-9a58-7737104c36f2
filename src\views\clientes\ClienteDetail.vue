<template>
  <div class="cliente-detail">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">Detalle del Cliente #{{ id }}</h1>
          <p class="text-muted">Información detallada del cliente</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body text-center py-5">
              <i class="fas fa-user fa-3x text-muted mb-3"></i>
              <h4>Detalle del Cliente</h4>
              <p class="text-muted">Esta vista será implementada próximamente</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ClienteDetail',
  props: {
    id: {
      type: String,
      required: true
    }
  }
}
</script>
