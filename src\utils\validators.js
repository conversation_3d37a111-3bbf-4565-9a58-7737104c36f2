// Validaciones comunes

// Validar email
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Validar teléfono español
export const isValidPhone = (phone) => {
  const phoneRegex = /^(\+34|0034|34)?[6789]\d{8}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

// Validar DNI/NIE español
export const isValidDNI = (dni) => {
  const dniRegex = /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i
  const nieRegex = /^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i
  
  if (!dniRegex.test(dni) && !nieRegex.test(dni)) {
    return false
  }
  
  const letters = 'TRWAGMYFPDXBNJZSQVHLCKE'
  let number = dni.substring(0, 8)
  
  if (nieRegex.test(dni)) {
    const firstChar = dni.charAt(0)
    if (firstChar === 'X') number = '0' + number
    else if (firstChar === 'Y') number = '1' + number
    else if (firstChar === 'Z') number = '2' + number
  }
  
  const expectedLetter = letters[parseInt(number) % 23]
  return expectedLetter === dni.charAt(8).toUpperCase()
}

// Validar código postal español
export const isValidPostalCode = (postalCode) => {
  const postalCodeRegex = /^[0-5]\d{4}$/
  return postalCodeRegex.test(postalCode)
}

// Validar IBAN
export const isValidIBAN = (iban) => {
  const ibanRegex = /^ES\d{2}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\s?\d{4}$/
  return ibanRegex.test(iban.replace(/\s/g, ''))
}

// Validar que un campo no esté vacío
export const isRequired = (value) => {
  return value !== null && value !== undefined && value.toString().trim() !== ''
}

// Validar longitud mínima
export const minLength = (value, min) => {
  return value && value.toString().length >= min
}

// Validar longitud máxima
export const maxLength = (value, max) => {
  return !value || value.toString().length <= max
}

// Validar que sea un número
export const isNumber = (value) => {
  return !isNaN(value) && !isNaN(parseFloat(value))
}

// Validar que sea un número positivo
export const isPositiveNumber = (value) => {
  return isNumber(value) && parseFloat(value) > 0
}

// Validar que sea un entero
export const isInteger = (value) => {
  return Number.isInteger(Number(value))
}

// Validar rango de fechas
export const isValidDateRange = (startDate, endDate) => {
  if (!startDate || !endDate) return true
  return new Date(startDate) <= new Date(endDate)
}

// Validar que la fecha no sea pasada
export const isNotPastDate = (date) => {
  if (!date) return true
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return new Date(date) >= today
}

// Validar formato de archivo
export const isValidFileType = (file, allowedTypes) => {
  if (!file || !allowedTypes) return false
  
  const fileExtension = file.name.split('.').pop().toLowerCase()
  return allowedTypes.includes(fileExtension)
}

// Validar tamaño de archivo
export const isValidFileSize = (file, maxSizeInBytes) => {
  if (!file) return false
  return file.size <= maxSizeInBytes
}

// Validar URL
export const isValidURL = (url) => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

// Validar contraseña segura
export const isStrongPassword = (password) => {
  // Al menos 8 caracteres, una mayúscula, una minúscula, un número
  const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  return strongPasswordRegex.test(password)
}

// Validaciones compuestas para formularios
export const validateClienteForm = (cliente) => {
  const errors = {}
  
  if (!isRequired(cliente.nombre)) {
    errors.nombre = 'El nombre es obligatorio'
  }
  
  if (!isRequired(cliente.email)) {
    errors.email = 'El email es obligatorio'
  } else if (!isValidEmail(cliente.email)) {
    errors.email = 'El email no es válido'
  }
  
  if (cliente.telefono && !isValidPhone(cliente.telefono)) {
    errors.telefono = 'El teléfono no es válido'
  }
  
  if (cliente.dni && !isValidDNI(cliente.dni)) {
    errors.dni = 'El DNI/NIE no es válido'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

export const validateAlquilerForm = (alquiler) => {
  const errors = {}
  
  if (!isRequired(alquiler.cliente_id)) {
    errors.cliente_id = 'Debe seleccionar un cliente'
  }
  
  if (!isRequired(alquiler.valor)) {
    errors.valor = 'El valor del alquiler es obligatorio'
  } else if (!isPositiveNumber(alquiler.valor)) {
    errors.valor = 'El valor debe ser un número positivo'
  }
  
  if (!isRequired(alquiler.fecha_inicio)) {
    errors.fecha_inicio = 'La fecha de inicio es obligatoria'
  }
  
  if (!isValidDateRange(alquiler.fecha_inicio, alquiler.fecha_fin)) {
    errors.fecha_fin = 'La fecha de fin debe ser posterior a la fecha de inicio'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}
