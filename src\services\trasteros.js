import api from './api'

export const trasterosService = {
  // Obtener todos los trasteros
  getAll(params = {}) {
    return api.get('/trasteros', { params })
  },

  // Obtener trastero por ID
  getById(id) {
    return api.get(`/trasteros/${id}`)
  },

  // Crear nuevo trastero
  create(data) {
    return api.post('/trasteros', data)
  },

  // Actualizar trastero
  update(id, data) {
    return api.put(`/trasteros/${id}`, data)
  },

  // Eliminar trastero
  delete(id) {
    return api.delete(`/trasteros/${id}`)
  },

  // Obtener trasteros disponibles
  getDisponibles() {
    return api.get('/trasteros-disponibles')
  },

  // Obtener trasteros ocupados
  getOcupados() {
    return api.get('/trasteros-ocupados')
  },

  // Obtener trasteros por tipo
  getPorTipo(tipoId) {
    return api.get(`/trasteros-tipo/${tipoId}`)
  }
}
