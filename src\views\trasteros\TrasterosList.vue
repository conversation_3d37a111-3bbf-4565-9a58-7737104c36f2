<template>
  <div class="trasteros-list">
    <div class="container-fluid">
      <div class="row mb-4">
        <div class="col-12">
          <h1 class="h3 mb-0">Gestión de Trasteros</h1>
          <p class="text-muted">Administra todos los trasteros del sistema</p>
        </div>
      </div>
      
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-body text-center py-5">
              <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
              <h4>Vista de Trasteros</h4>
              <p class="text-muted">Esta vista será implementada próximamente</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TrasterosList'
}
</script>
